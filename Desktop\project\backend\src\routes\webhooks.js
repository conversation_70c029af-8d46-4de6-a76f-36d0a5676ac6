const express = require('express');
const twilio = require('twilio');
const { twilioConfig } = require('../config/twilio');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { logger } = require('../utils/logger');

const router = express.Router();

// Import call storage from calls route (in production, use shared database)
const activeCalls = new Map();
const callHistory = new Map();

// Middleware to verify Twilio webhook signature
const verifyTwilioSignature = (req, res, next) => {
  if (process.env.NODE_ENV === 'production') {
    const twilioSignature = req.headers['x-twilio-signature'];
    const url = `${twilioConfig.webhookBaseUrl}${req.originalUrl}`;

    if (
      !twilio.validateRequest(
        twilioConfig.authToken,
        twilioSignature,
        url,
        req.body
      )
    ) {
      logger.warn('Invalid Twilio webhook signature', {
        url,
        signature: twilioSignature,
      });
      return res.status(403).json({ error: 'Invalid signature' });
    }
  }
  next();
};

// POST /api/webhooks/call-status - Handle call status updates
router.post(
  '/call-status',
  verifyTwilioSignature,
  asyncHandler(async (req, res) => {
    const {
      CallSid,
      CallStatus,
      From,
      To,
      Duration,
      RecordingUrl,
      RecordingSid,
      AnsweredBy,
    } = req.body;

    logger.info('Call status update received:', {
      CallSid,
      CallStatus,
      From,
      To,
      Duration,
      AnsweredBy,
    });

    // Find the call by Twilio SID
    let callData = null;
    let callId = null;

    for (const [id, data] of callHistory.entries()) {
      if (data.twilioCallSid === CallSid) {
        callData = data;
        callId = id;
        break;
      }
    }

    if (callData) {
      // Update call status
      callData.status = CallStatus;
      callData.updatedAt = new Date().toISOString();

      // Handle specific status updates
      switch (CallStatus) {
        case 'ringing':
          callData.ringingAt = new Date().toISOString();
          break;

        case 'in-progress':
          callData.answeredAt = new Date().toISOString();
          callData.answeredBy = AnsweredBy;
          break;

        case 'completed':
          callData.completedAt = new Date().toISOString();
          callData.duration = Duration;
          if (RecordingUrl) {
            callData.recordingUrl = RecordingUrl;
            callData.recordingSid = RecordingSid;
          }
          // Remove from active calls
          activeCalls.delete(callId);
          break;

        case 'busy':
        case 'failed':
        case 'no-answer':
        case 'canceled':
          callData.endedAt = new Date().toISOString();
          callData.failureReason = CallStatus;
          // Remove from active calls
          activeCalls.delete(callId);
          break;
      }

      // Update both active calls and history
      if (activeCalls.has(callId)) {
        activeCalls.set(callId, callData);
      }
      callHistory.set(callId, callData);

      logger.info(`Call ${callId} status updated to ${CallStatus}`);
    } else {
      logger.warn(`Call not found for Twilio SID: ${CallSid}`);
    }

    // Respond with empty TwiML to acknowledge receipt
    res.type('text/xml');
    res.send('<?xml version="1.0" encoding="UTF-8"?><Response></Response>');
  })
);

// POST /api/webhooks/twiml/outbound - Generate TwiML for outbound-only calls
router.post(
  '/twiml/outbound',
  verifyTwilioSignature,
  asyncHandler(async (req, res) => {
    const { CallSid, From, To, Direction } = req.body;

    logger.info('TwiML requested for outbound call:', {
      CallSid,
      From,
      To,
      Direction,
    });

    // Ensure this is an outbound call only
    if (Direction !== 'outbound-api') {
      logger.warn(
        `Rejecting non-outbound call: ${Direction} from ${From} to ${To}`
      );
      const twiml = new twilio.twiml.VoiceResponse();
      twiml.say(
        {
          voice: 'alice',
          language: 'en-US',
        },
        'This service is not available for incoming calls. Goodbye.'
      );
      twiml.hangup();

      res.type('text/xml');
      res.send(twiml.toString());
      return;
    }

    // Generate TwiML response for outbound calls
    const twiml = new twilio.twiml.VoiceResponse();

    // Brief greeting for the lead who answers
    twiml.say(
      {
        voice: 'alice',
        language: 'en-US',
      },
      'Hello, you have an incoming call. Please hold while we connect you.'
    );

    // Add a short pause
    twiml.pause({ length: 2 });

    // Connect the call directly - this creates a simple bridge
    // The lead can talk directly to whoever initiated the call
    twiml.say(
      {
        voice: 'alice',
        language: 'en-US',
      },
      'You are now connected.'
    );

    res.type('text/xml');
    res.send(twiml.toString());
  })
);

// POST /api/webhooks/twiml/inbound - Reject all inbound calls (outbound-only system)
router.post(
  '/twiml/inbound',
  verifyTwilioSignature,
  asyncHandler(async (req, res) => {
    const { CallSid, From, To } = req.body;

    logger.info('Rejecting inbound call (outbound-only system):', {
      CallSid,
      From,
      To,
    });

    const twiml = new twilio.twiml.VoiceResponse();

    // Politely reject inbound calls
    twiml.say(
      {
        voice: 'alice',
        language: 'en-US',
      },
      'Thank you for calling. This number is for outbound calls only and cannot receive incoming calls. Please contact us through our main customer service line. Goodbye.'
    );

    // Hang up the call
    twiml.hangup();

    res.type('text/xml');
    res.send(twiml.toString());
  })
);

// POST /api/webhooks/twiml/hold - Hold music TwiML
router.post(
  '/twiml/hold',
  verifyTwilioSignature,
  asyncHandler(async (req, res) => {
    const twiml = new twilio.twiml.VoiceResponse();

    twiml.play('http://twimlets.com/holdmusic?Bucket=com.twilio.music.ambient');
    twiml.redirect(`${twilioConfig.webhookBaseUrl}/api/webhooks/twiml/hold`);

    res.type('text/xml');
    res.send(twiml.toString());
  })
);

// POST /api/webhooks/recording-status - Handle recording status updates
router.post(
  '/recording-status',
  verifyTwilioSignature,
  asyncHandler(async (req, res) => {
    const {
      CallSid,
      RecordingSid,
      RecordingUrl,
      RecordingStatus,
      RecordingDuration,
    } = req.body;

    logger.info('Recording status update:', {
      CallSid,
      RecordingSid,
      RecordingStatus,
      RecordingDuration,
    });

    // Find and update the call with recording information
    for (const [callId, callData] of callHistory.entries()) {
      if (callData.twilioCallSid === CallSid) {
        callData.recordingSid = RecordingSid;
        callData.recordingUrl = RecordingUrl;
        callData.recordingStatus = RecordingStatus;
        callData.recordingDuration = RecordingDuration;
        callData.updatedAt = new Date().toISOString();

        callHistory.set(callId, callData);
        logger.info(`Recording info updated for call ${callId}`);
        break;
      }
    }

    res.status(200).send('OK');
  })
);

// POST /api/webhooks/conference-status - Handle conference status updates
router.post(
  '/conference-status',
  verifyTwilioSignature,
  asyncHandler(async (req, res) => {
    const { ConferenceSid, FriendlyName, StatusCallbackEvent, Timestamp } =
      req.body;

    logger.info('Conference status update:', {
      ConferenceSid,
      FriendlyName,
      StatusCallbackEvent,
      Timestamp,
    });

    res.status(200).send('OK');
  })
);

module.exports = router;
