// API service for Twilio VoIP backend integration
const API_BASE_URL = __DEV__
  ? 'http://**************:3002/api'
  : 'https://your-production-api.com/api';

export interface CallData {
  callId: string;
  twilioCallSid: string;
  leadId: string;
  leadName: string;
  leadPhoneNumber: string;
  twilioPhoneNumber: string;
  callType: 'outbound' | 'inbound';
  status: 'initiated' | 'ringing' | 'in-progress' | 'completed' | 'failed' | 'busy' | 'no-answer' | 'canceled';
  recordCall: boolean;
  createdAt: string;
  updatedAt: string;
  answeredAt?: string;
  completedAt?: string;
  duration?: string;
  recordingUrl?: string;
  recordingSid?: string;
  failureReason?: string;
}

export interface CallInitiateRequest {
  leadId: string;
  leadPhoneNumber: string;
  leadName: string;
  callType?: 'outbound' | 'inbound';
  recordCall?: boolean;
}

export interface CallInitiateResponse {
  success: boolean;
  data: {
    callId: string;
    twilioCallSid: string;
    status: string;
    leadName: string;
    message: string;
  };
}

export interface CallStatsResponse {
  success: boolean;
  data: {
    totalCalls: number;
    completedCalls: number;
    failedCalls: number;
    activeCalls: number;
    averageDuration: number;
    totalDuration: number;
    recordedCalls: number;
  };
}

class ApiService {
  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Call Management APIs
  async initiateCall(request: CallInitiateRequest): Promise<CallInitiateResponse> {
    return this.makeRequest<CallInitiateResponse>('/calls/initiate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getCallDetails(callId: string): Promise<{ success: boolean; data: CallData }> {
    return this.makeRequest<{ success: boolean; data: CallData }>(`/calls/${callId}`);
  }

  async endCall(callId: string): Promise<{ success: boolean; data: { callId: string; status: string; message: string } }> {
    return this.makeRequest(`/calls/${callId}/end`, {
      method: 'POST',
    });
  }

  async getCallHistory(params?: {
    page?: number;
    limit?: number;
    status?: string;
    leadId?: string;
  }): Promise<{
    success: boolean;
    data: {
      calls: CallData[];
      pagination: {
        currentPage: number;
        totalPages: number;
        totalCalls: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    };
  }> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.status) queryParams.append('status', params.status);
    if (params?.leadId) queryParams.append('leadId', params.leadId);

    const endpoint = `/calls${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest(endpoint);
  }

  async getActiveCalls(): Promise<{
    success: boolean;
    data: {
      activeCalls: CallData[];
      count: number;
    };
  }> {
    return this.makeRequest('/calls/active/list');
  }

  async getCallStats(params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<CallStatsResponse> {
    const queryParams = new URLSearchParams();
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);

    const endpoint = `/calls/stats/summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest(endpoint);
  }

  // Recording APIs
  async getCallRecording(callId: string): Promise<{
    success: boolean;
    data: {
      callId: string;
      recordingSid: string;
      recordingUrl: string;
      duration: number;
      status: string;
      dateCreated: string;
      fileSize: number;
    };
  }> {
    return this.makeRequest(`/calls/${callId}/recording`);
  }

  async deleteCallRecording(callId: string): Promise<{
    success: boolean;
    data: {
      callId: string;
      message: string;
    };
  }> {
    return this.makeRequest(`/calls/${callId}/recording`, {
      method: 'DELETE',
    });
  }

  // Health check
  async healthCheck(): Promise<{
    status: string;
    timestamp: string;
    service: string;
  }> {
    const url = `${API_BASE_URL.replace('/api', '')}/health`;
    return this.makeRequest(url);
  }
}

export const apiService = new ApiService();
export default apiService;
