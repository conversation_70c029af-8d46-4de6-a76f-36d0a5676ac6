# Twilio VoIP Integration Setup Guide

This guide will walk you through setting up the complete Twilio VoIP integration for real-time calling between your app and lead phone numbers.

## Overview

The integration consists of:
- **Backend API**: Node.js/Express server with Twilio SDK
- **React Native App**: Updated call interface with real API integration
- **Twilio Services**: VoIP calling, webhooks, and recording

## Step 1: Twilio Account Setup

### 1.1 Create Twilio Account
1. Go to [https://www.twilio.com/try-twilio](https://www.twilio.com/try-twilio)
2. Sign up for a free account
3. Verify your email and phone number

### 1.2 Get Twilio Credentials
1. Go to Twilio Console Dashboard
2. Note down your **Account SID** and **Auth Token**
3. Keep these secure - you'll need them for the backend

### 1.3 Get a Twilio Phone Number
1. In Twilio Console, go to **Phone Numbers** > **Manage** > **Buy a number**
2. Choose a number with **Voice** capabilities
3. Purchase the number (free with trial account)
4. Note down the phone number in E.164 format (e.g., +**********)

### 1.4 Configure Webhooks (After Backend Setup)
You'll configure these after setting up ngrok in Step 3.

## Step 2: Backend Setup

### 2.1 Install Dependencies
```bash
cd backend
npm install
```

### 2.2 Environment Configuration
1. Copy the environment template:
```bash
cp .env.example .env
```

2. Edit `.env` file with your Twilio credentials:
```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=+**********

# Server Configuration
PORT=3001
NODE_ENV=development

# Will be updated after ngrok setup
WEBHOOK_BASE_URL=http://localhost:3001
```

### 2.3 Test Backend
```bash
npm run dev
```

You should see:
```
Twilio VoIP Backend server running on port 3001
Environment: development
Twilio connection established successfully
```

## Step 3: ngrok Setup (For Local Development)

### 3.1 Install ngrok
```bash
npm install -g ngrok
```

### 3.2 Expose Local Server
In a new terminal:
```bash
ngrok http 3001
```

You'll see output like:
```
Forwarding    https://abc123.ngrok.io -> http://localhost:3001
```

### 3.3 Update Environment
1. Copy the ngrok HTTPS URL (e.g., `https://abc123.ngrok.io`)
2. Update your `.env` file:
```env
WEBHOOK_BASE_URL=https://abc123.ngrok.io
```
3. Restart your backend server

## Step 4: Configure Twilio Webhooks

### 4.1 Set Phone Number Webhooks
1. Go to Twilio Console > **Phone Numbers** > **Manage** > **Active numbers**
2. Click on your Twilio phone number
3. In the **Voice** section, set:
   - **A call comes in**: Webhook `https://your-ngrok-url.ngrok.io/api/webhooks/twiml/inbound`
   - **HTTP**: POST

### 4.2 Test Webhook Connection
1. Check your backend logs
2. Make a test call to your Twilio number
3. You should see webhook requests in the logs

## Step 5: React Native App Setup

### 5.1 Install Dependencies
The API service is already created. No additional dependencies needed.

### 5.2 Update API Base URL (if needed)
In `services/api.ts`, the API automatically detects development mode:
```typescript
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3001/api' 
  : 'https://your-production-api.com/api';
```

For local development, this should work automatically.

## Step 6: Testing the Integration

### 6.1 Start Both Services
1. **Backend**: `cd backend && npm run dev`
2. **React Native**: `cd .. && npm run dev`

### 6.2 Test Call Flow
1. Open the React Native app
2. Navigate to a lead detail page
3. Tap the call button
4. The app should:
   - Show "Starting..." button state
   - Initiate call via backend API
   - Display call status updates
   - Show call timer when connected

### 6.3 Monitor Logs
Watch the backend logs for:
- Call initiation requests
- Twilio API calls
- Webhook status updates
- Any errors

## Step 7: Production Deployment

### 7.1 Backend Deployment
1. Deploy to a cloud service (Heroku, AWS, etc.)
2. Update environment variables with production values
3. Set `NODE_ENV=production`
4. Update `WEBHOOK_BASE_URL` to your production domain

### 7.2 Update Twilio Webhooks
Update your Twilio phone number webhooks to point to your production URLs.

### 7.3 React Native App
Update the production API URL in `services/api.ts`.

## Troubleshooting

### Common Issues

#### 1. "Twilio connection failed"
- Check Account SID and Auth Token
- Ensure credentials are correct in `.env`
- Verify Twilio account is active

#### 2. "Call initiation failed"
- Verify phone number format (E.164: +**********)
- Check if lead phone number is verified (trial accounts)
- Ensure Twilio number has voice capabilities

#### 3. "Webhook not receiving calls"
- Verify ngrok is running and URL is correct
- Check `WEBHOOK_BASE_URL` in `.env`
- Ensure Twilio phone number webhook is configured

#### 4. "Call connects but no audio"
- Check TwiML generation in logs
- Verify conference room creation
- Test with different phone numbers

#### 5. "Recording not working"
- Check Twilio account recording permissions
- Verify recording webhook URL
- Check recording status in call logs

### Debug Steps

1. **Check Backend Health**:
   ```bash
   curl http://localhost:3001/health
   ```

2. **Test API Endpoint**:
   ```bash
   curl -X POST http://localhost:3001/api/calls/initiate \
     -H "Content-Type: application/json" \
     -d '{"leadId":"test","leadPhoneNumber":"+**********","leadName":"Test Lead"}'
   ```

3. **Check Logs**:
   - Backend logs: Console output
   - Twilio logs: Twilio Console > Monitor > Logs
   - ngrok logs: ngrok web interface (http://localhost:4040)

### Support Resources

- **Twilio Documentation**: https://www.twilio.com/docs
- **Twilio Console**: https://console.twilio.com
- **ngrok Documentation**: https://ngrok.com/docs
- **Backend API Documentation**: See `backend/README.md`

## Security Notes

- Never commit `.env` files to version control
- Use environment variables for all sensitive data
- Enable webhook signature verification in production
- Implement proper authentication for production APIs
- Regularly rotate Twilio Auth Tokens

## Next Steps

After successful setup:
1. Implement user authentication
2. Add call history UI in React Native
3. Set up call analytics dashboard
4. Implement call queuing for multiple agents
5. Add SMS integration
6. Set up monitoring and alerts
