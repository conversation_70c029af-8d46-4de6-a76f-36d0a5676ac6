import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  SafeAreaView,
  Image,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import { ChevronRight, Search, Filter } from 'lucide-react-native';
import AnimatedButton from '@/components/AnimatedButton';

// Enhanced mock leads data with more realistic information
const mockLeads = [
  { 
    id: '1', 
    title: 'Downtown Luxury Condo Inquiry', 
    email: '<EMAIL>', 
    phone: '+1234567890',
    priority: 'high',
    source: 'Website',
    date: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop'
  },
  { 
    id: '2', 
    title: 'Business Insurance Quote', 
    email: '<EMAIL>', 
    phone: '+1234567891',
    priority: 'medium',
    source: 'Referral',
    date: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop'
  },
  { 
    id: '3', 
    title: 'Investment Portfolio Review', 
    email: '<EMAIL>', 
    phone: '+1234567892',
    priority: 'high',
    source: 'LinkedIn',
    date: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop'
  },
  { 
    id: '4', 
    title: 'First-Time Home Buyer', 
    email: '<EMAIL>', 
    phone: '+1234567893',
    priority: 'medium',
    source: 'Google Ads',
    date: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
    avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop'
  },
  { 
    id: '5', 
    title: 'Commercial Property Lease', 
    email: '<EMAIL>', 
    phone: '+1234567894',
    priority: 'low',
    source: 'Walk-in',
    date: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop'
  },
  { 
    id: '6', 
    title: 'Retirement Planning Session', 
    email: '<EMAIL>', 
    phone: '+1234567895',
    priority: 'high',
    source: 'Email Campaign',
    date: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop'
  },
];

export default function LeadsListScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');

  const handleLeadPress = (lead: typeof mockLeads[0]) => {
    router.push({
      pathname: '/lead-detail',
      params: { leadId: lead.id }
    });
  };

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate API refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#FF3B30';
      case 'medium': return '#FF9500';
      case 'low': return '#34C759';
      default: return '#8E8E93';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const filteredLeads = filter === 'all' 
    ? mockLeads 
    : mockLeads.filter(lead => lead.priority === filter);

  const renderLeadItem = ({ item }: { item: typeof mockLeads[0] }) => (
    <AnimatedButton
      title=""
      onPress={() => handleLeadPress(item)}
      style={styles.leadCard}
    >
      <View style={styles.leadContent}>
        <View style={styles.leadHeader}>
          <Image source={{ uri: item.avatar }} style={styles.avatar} />
          <View style={styles.leadInfo}>
            <Text style={styles.leadTitle} numberOfLines={1}>
              {item.title}
            </Text>
            <View style={styles.leadMeta}>
              <View style={[styles.priorityDot, { backgroundColor: getPriorityColor(item.priority) }]} />
              <Text style={styles.leadSource}>{item.source}</Text>
              <Text style={styles.leadDate}>{formatTimeAgo(item.date)}</Text>
            </View>
          </View>
        </View>
        <ChevronRight size={20} color="#C7C7CC" />
      </View>
    </AnimatedButton>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{mockLeads.length}</Text>
          <Text style={styles.statLabel}>Total Leads</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {mockLeads.filter(l => l.priority === 'high').length}
          </Text>
          <Text style={styles.statLabel}>High Priority</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {mockLeads.filter(l => 
              new Date().getTime() - l.date.getTime() < 24 * 60 * 60 * 1000
            ).length}
          </Text>
          <Text style={styles.statLabel}>Today</Text>
        </View>
      </View>

      <View style={styles.filtersContainer}>
        {['all', 'high', 'medium', 'low'].map((filterOption) => (
          <AnimatedButton
            key={filterOption}
            title={filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
            onPress={() => setFilter(filterOption as any)}
            variant={filter === filterOption ? 'primary' : 'secondary'}
            style={[
              styles.filterButton,
              filter === filterOption && styles.activeFilterButton
            ]}
            textStyle={styles.filterButtonText}
          />
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View>
            <Text style={styles.headerTitle}>Leads</Text>
            <Text style={styles.headerSubtitle}>
              {filteredLeads.length} {filter !== 'all' ? filter + ' priority' : 'active'} leads
            </Text>
          </View>
          <View style={styles.headerActions}>
            <AnimatedButton
              title=""
              onPress={() => {}}
              style={styles.headerButton}
            >
              <Search size={20} color="#007AFF" />
            </AnimatedButton>
            <AnimatedButton
              title=""
              onPress={() => {}}
              style={styles.headerButton}
            >
              <Filter size={20} color="#007AFF" />
            </AnimatedButton>
          </View>
        </View>
      </View>
      
      <FlatList
        data={filteredLeads}
        renderItem={renderLeadItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#007AFF"
          />
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#8E8E93',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    backgroundColor: '#F2F2F7',
    width: 44,
    height: 44,
    borderRadius: 12,
    shadowOpacity: 0,
    elevation: 0,
  },
  headerContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#007AFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8E8E93',
    textAlign: 'center',
  },
  filtersContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    shadowOpacity: 0,
    elevation: 0,
  },
  activeFilterButton: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  listContainer: {
    paddingBottom: 16,
  },
  leadCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  leadContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leadHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  leadInfo: {
    flex: 1,
  },
  leadTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 6,
  },
  leadMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  leadSource: {
    fontSize: 12,
    color: '#8E8E93',
    marginRight: 8,
  },
  leadDate: {
    fontSize: 12,
    color: '#8E8E93',
  },
});

// Export mock leads for use in other screens
export { mockLeads };