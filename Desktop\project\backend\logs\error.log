[2025-06-23T11:26:44.867Z] [ERROR] Missing required environment variables: TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER 
[2025-06-23T12:21:50.087Z] [ERROR] Failed to initiate call: [{"status":400,"code":21211,"moreInfo":"https://www.twilio.com/docs/errors/21211"}]
[2025-06-23T12:21:50.089Z] [ERROR] Error occurred: [{"message":"T<PERSON><PERSON> Error: The phone number you are attempting to call, +***********, is not valid.","stack":"Error: Twi<PERSON>rror: The phone number you are attempting to call, +***********, is not valid.\n    at C:\\Users\\<USER>\\Desktop\\project\\backend\\src\\routes\\calls.js:109:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","url":"/api/calls/initiate","method":"POST","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}]
[2025-06-23T13:26:20.397Z] [ERROR] Error occurred: [{"message":"Call not found","stack":"Error: Call not found\n    at C:\\Users\\<USER>\\Desktop\\project\\backend\\src\\routes\\calls.js:394:13\n    at C:\\Users\\<USER>\\Desktop\\project\\backend\\src\\middleware\\errorHandler.js:69:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)","url":"/api/calls/bc5cdac6-1a75-4acc-92d6-fde19cf879c5/join","method":"POST","ip":"**************","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}]
[2025-06-23T13:45:27.914Z] [ERROR] Error occurred: [{"message":"\"callType\" must be one of [outbound, inbound]","stack":"Error: \"callType\" must be one of [outbound, inbound]\n    at C:\\Users\\<USER>\\Desktop\\project\\backend\\src\\middleware\\validation.js:51:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\project\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","url":"/api/calls/conference","method":"POST","ip":"**************","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}]
