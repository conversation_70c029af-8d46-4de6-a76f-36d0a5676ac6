[2025-06-23T11:26:54.292Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:26:54.295Z] [INFO] Environment: development 
[2025-06-23T11:28:35.993Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:28:35.995Z] [INFO] Environment: development 
[2025-06-23T11:33:08.604Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:33:08.606Z] [INFO] Environment: development 
[2025-06-23T11:33:17.896Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:33:17.899Z] [INFO] Environment: development 
[2025-06-23T11:35:27.005Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:35:27.008Z] [INFO] Environment: development 
[2025-06-23T11:35:57.616Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:35:57.619Z] [INFO] Environment: development 
[2025-06-23T11:36:31.438Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:36:31.440Z] [INFO] Environment: development 
[2025-06-23T11:43:26.154Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T11:43:26.156Z] [INFO] Environment: development 
[2025-06-23T12:02:28.193Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T12:02:28.195Z] [INFO] Environment: development 
[2025-06-23T12:19:19.292Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T12:19:19.295Z] [INFO] Environment: development 
[2025-06-23T12:19:44.968Z] [INFO] ::1 - - [23/Jun/2025:12:19:44 +0000] "GET /health HTTP/1.1" 200 86 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T12:21:49.350Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T12:21:50.093Z] [INFO] ::1 - - [23/Jun/2025:12:21:50 +0000] "POST /api/calls/initiate HTTP/1.1" 400 396 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T12:22:54.698Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T12:22:55.353Z] [INFO] Call initiated: 6430a51a-9dad-4549-a85c-004cdbd883f7 [{"twilioCallSid":"CA1d0ad78c5e38a6fa8b4e5c77ffa3cc9c","leadId":"test-123","leadPhoneNumber":"+923360150089"}]
[2025-06-23T12:22:55.355Z] [INFO] ::1 - - [23/Jun/2025:12:22:55 +0000] "POST /api/calls/initiate HTTP/1.1" 201 210 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T12:24:18.680Z] [INFO] ::1 - - [23/Jun/2025:12:24:18 +0000] "GET /api/calls/6430a51a-9dad-4549-a85c-004cdbd883f7 HTTP/1.1" 200 377 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T12:24:34.335Z] [INFO] ::1 - - [23/Jun/2025:12:24:34 +0000] "GET /api/calls/stats/summary HTTP/1.1" 200 147 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T12:34:35.472Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T12:34:35.474Z] [INFO] Environment: development 
[2025-06-23T12:34:50.283Z] [INFO] Twilio VoIP Backend server running on port 3001 
[2025-06-23T12:34:50.285Z] [INFO] Environment: development 
[2025-06-23T12:36:40.859Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T12:36:40.861Z] [INFO] Environment: development 
[2025-06-23T12:43:09.393Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T12:43:10.150Z] [INFO] Call initiated: 38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a [{"twilioCallSid":"CAa6d7a05553b3585cdaf601345bede321","leadId":"1","leadPhoneNumber":"+923360150089"}]
[2025-06-23T12:43:10.154Z] [INFO] ::1 - - [23/Jun/2025:12:43:10 +0000] "POST /api/calls/initiate HTTP/1.1" 201 230 "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:12.168Z] [INFO] ::1 - - [23/Jun/2025:12:43:12 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 200 390 "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:14.165Z] [INFO] ::1 - - [23/Jun/2025:12:43:14 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:16.168Z] [INFO] ::1 - - [23/Jun/2025:12:43:16 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:18.164Z] [INFO] ::1 - - [23/Jun/2025:12:43:18 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:20.160Z] [INFO] ::1 - - [23/Jun/2025:12:43:20 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:22.168Z] [INFO] ::1 - - [23/Jun/2025:12:43:22 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:24.163Z] [INFO] ::1 - - [23/Jun/2025:12:43:24 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:26.166Z] [INFO] ::1 - - [23/Jun/2025:12:43:26 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:28.167Z] [INFO] ::1 - - [23/Jun/2025:12:43:28 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:30.168Z] [INFO] ::1 - - [23/Jun/2025:12:43:30 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:32.168Z] [INFO] ::1 - - [23/Jun/2025:12:43:32 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:34.159Z] [INFO] ::1 - - [23/Jun/2025:12:43:34 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:36.161Z] [INFO] ::1 - - [23/Jun/2025:12:43:36 +0000] "GET /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a HTTP/1.1" 304 - "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:38.111Z] [INFO] Call ended: 38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a [{"twilioCallSid":"CAa6d7a05553b3585cdaf601345bede321"}]
[2025-06-23T12:43:38.112Z] [INFO] ::1 - - [23/Jun/2025:12:43:38 +0000] "POST /api/calls/38f0fd1f-bd69-40fb-8841-9c37a9fd6b3a/end HTTP/1.1" 200 130 "http://localhost:8081/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:42.291Z] [INFO] ::1 - - [23/Jun/2025:12:43:42 +0000] "GET /api HTTP/1.1" 404 27 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:43:42.961Z] [INFO] ::1 - - [23/Jun/2025:12:43:42 +0000] "GET /favicon.ico HTTP/1.1" 404 27 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T12:47:32.027Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T12:47:32.029Z] [INFO] Environment: development 
[2025-06-23T12:47:59.619Z] [INFO] ::ffff:************** - - [23/Jun/2025:12:47:59 +0000] "GET /health HTTP/1.1" 200 86 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T12:48:11.742Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T12:48:11.744Z] [INFO] Environment: development 
[2025-06-23T12:48:11.745Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:06:41.141Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T13:06:42.270Z] [INFO] Call initiated: bc5cdac6-1a75-4acc-92d6-fde19cf879c5 [{"twilioCallSid":"CA313a333baf063a5459fdbc8b0ceeff98","leadId":"test-mobile","leadPhoneNumber":"+923360150089"}]
[2025-06-23T13:06:42.274Z] [INFO] ************** - - [23/Jun/2025:13:06:42 +0000] "POST /api/calls/initiate HTTP/1.1" 201 212 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:09:08.573Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T13:09:09.639Z] [INFO] Call initiated: 46189e90-7927-469f-8dac-9b020b75f77e [{"twilioCallSid":"CAdac89080c4ff2c2f4ed03bc1382aab07","leadId":"1","leadPhoneNumber":"+923360150089"}]
[2025-06-23T13:09:09.640Z] [INFO] ************* - - [23/Jun/2025:13:09:09 +0000] "POST /api/calls/initiate HTTP/1.1" 201 230 "-" "okhttp/4.12.0" 
[2025-06-23T13:09:11.714Z] [INFO] ************* - - [23/Jun/2025:13:09:11 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 200 390 "-" "okhttp/4.12.0" 
[2025-06-23T13:09:13.723Z] [INFO] ************* - - [23/Jun/2025:13:09:13 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:15.729Z] [INFO] ************* - - [23/Jun/2025:13:09:15 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:17.731Z] [INFO] ************* - - [23/Jun/2025:13:09:17 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:19.738Z] [INFO] ************* - - [23/Jun/2025:13:09:19 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:22.387Z] [INFO] ************* - - [23/Jun/2025:13:09:22 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:24.390Z] [INFO] ************* - - [23/Jun/2025:13:09:24 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:26.390Z] [INFO] ************* - - [23/Jun/2025:13:09:26 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:28.415Z] [INFO] ************* - - [23/Jun/2025:13:09:28 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:30.443Z] [INFO] ************* - - [23/Jun/2025:13:09:30 +0000] "GET /api/calls/46189e90-7927-469f-8dac-9b020b75f77e HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:09:30.514Z] [INFO] Call ended: 46189e90-7927-469f-8dac-9b020b75f77e [{"twilioCallSid":"CAdac89080c4ff2c2f4ed03bc1382aab07"}]
[2025-06-23T13:09:30.515Z] [INFO] ************* - - [23/Jun/2025:13:09:30 +0000] "POST /api/calls/46189e90-7927-469f-8dac-9b020b75f77e/end HTTP/1.1" 200 130 "-" "okhttp/4.12.0" 
[2025-06-23T13:18:13.863Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:18:13.866Z] [INFO] Environment: development 
[2025-06-23T13:18:13.867Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:19:10.538Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:19:10.540Z] [INFO] Environment: development 
[2025-06-23T13:19:10.543Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:19:52.330Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:19:52.332Z] [INFO] Environment: development 
[2025-06-23T13:19:52.333Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:26:20.403Z] [INFO] ************** - - [23/Jun/2025:13:26:20 +0000] "POST /api/calls/bc5cdac6-1a75-4acc-92d6-fde19cf879c5/join HTTP/1.1" 404 1230 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:26:37.879Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T13:26:39.191Z] [INFO] Call initiated: 5be35f95-8bf3-4a94-bacf-b267209f2021 [{"twilioCallSid":"CAf70f22a594b5559f4e8836a334c8c7f3","leadId":"test-realtime","leadPhoneNumber":"+923360150089"}]
[2025-06-23T13:26:39.192Z] [INFO] ************** - - [23/Jun/2025:13:26:39 +0000] "POST /api/calls/initiate HTTP/1.1" 201 215 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:27:31.368Z] [INFO] Caller joining conference for call: 5be35f95-8bf3-4a94-bacf-b267209f2021 [{"callerCallSid":"CA069b9d19255fb4e4ea4c37b412ff29ab","callerPhone":"+923360150089","conferenceId":"CAf70f22a594b5559f4e8836a334c8c7f3"}]
[2025-06-23T13:27:31.370Z] [INFO] ************** - - [23/Jun/2025:13:27:31 +0000] "POST /api/calls/5be35f95-8bf3-4a94-bacf-b267209f2021/join HTTP/1.1" 200 189 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:29:24.606Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T13:29:25.257Z] [INFO] Call initiated: 29f54a2c-f62d-472e-867a-128a73bf1a1d [{"twilioCallSid":"CA649bb86b1875b149c4f46f3a3d91b8b9","leadId":"test-realtime-demo","leadPhoneNumber":"+923360150089"}]
[2025-06-23T13:29:25.258Z] [INFO] ************** - - [23/Jun/2025:13:29:25 +0000] "POST /api/calls/initiate HTTP/1.1" 201 220 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:30:12.681Z] [INFO] Caller joining conference for call: 29f54a2c-f62d-472e-867a-128a73bf1a1d [{"callerCallSid":"CAcc50fee294049efd6a517827f5bf34b9","callerPhone":"+923360150089","conferenceId":"CA649bb86b1875b149c4f46f3a3d91b8b9"}]
[2025-06-23T13:30:12.683Z] [INFO] ************** - - [23/Jun/2025:13:30:12 +0000] "POST /api/calls/29f54a2c-f62d-472e-867a-128a73bf1a1d/join HTTP/1.1" 200 189 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:36:16.698Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:36:16.700Z] [INFO] Environment: development 
[2025-06-23T13:36:16.701Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:39:09.255Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T13:39:09.899Z] [INFO] Call initiated: 8ee1511c-0ad8-4d6b-ab16-aa93625cef32 [{"twilioCallSid":"CAa5fdc2b3aeeb9e37aeea2081d5484f32","leadId":"1","leadPhoneNumber":"+923360150089"}]
[2025-06-23T13:39:09.902Z] [INFO] ************* - - [23/Jun/2025:13:39:09 +0000] "POST /api/calls/initiate HTTP/1.1" 201 230 "-" "okhttp/4.12.0" 
[2025-06-23T13:39:12.023Z] [INFO] ************* - - [23/Jun/2025:13:39:12 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 200 390 "-" "okhttp/4.12.0" 
[2025-06-23T13:39:14.028Z] [INFO] ************* - - [23/Jun/2025:13:39:14 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:16.027Z] [INFO] ************* - - [23/Jun/2025:13:39:16 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:18.059Z] [INFO] ************* - - [23/Jun/2025:13:39:18 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:20.028Z] [INFO] ************* - - [23/Jun/2025:13:39:20 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:22.059Z] [INFO] ************* - - [23/Jun/2025:13:39:22 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:24.057Z] [INFO] ************* - - [23/Jun/2025:13:39:24 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:26.048Z] [INFO] ************* - - [23/Jun/2025:13:39:26 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:28.066Z] [INFO] ************* - - [23/Jun/2025:13:39:28 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:30.085Z] [INFO] ************* - - [23/Jun/2025:13:39:30 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:32.105Z] [INFO] ************* - - [23/Jun/2025:13:39:32 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:34.094Z] [INFO] ************* - - [23/Jun/2025:13:39:34 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:36.137Z] [INFO] ************* - - [23/Jun/2025:13:39:36 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:38.121Z] [INFO] ************* - - [23/Jun/2025:13:39:38 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:40.165Z] [INFO] ************* - - [23/Jun/2025:13:39:40 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:42.102Z] [INFO] ************* - - [23/Jun/2025:13:39:42 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:44.120Z] [INFO] ************* - - [23/Jun/2025:13:39:44 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:46.127Z] [INFO] ************* - - [23/Jun/2025:13:39:46 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:48.130Z] [INFO] ************* - - [23/Jun/2025:13:39:48 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:50.115Z] [INFO] ************* - - [23/Jun/2025:13:39:50 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:52.140Z] [INFO] ************* - - [23/Jun/2025:13:39:52 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:54.148Z] [INFO] ************* - - [23/Jun/2025:13:39:54 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:56.154Z] [INFO] ************* - - [23/Jun/2025:13:39:56 +0000] "GET /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:39:57.677Z] [INFO] Call ended: 8ee1511c-0ad8-4d6b-ab16-aa93625cef32 [{"twilioCallSid":"CAa5fdc2b3aeeb9e37aeea2081d5484f32"}]
[2025-06-23T13:39:57.679Z] [INFO] ************* - - [23/Jun/2025:13:39:57 +0000] "POST /api/calls/8ee1511c-0ad8-4d6b-ab16-aa93625cef32/end HTTP/1.1" 200 130 "-" "okhttp/4.12.0" 
[2025-06-23T13:40:09.349Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T13:40:09.874Z] [INFO] Call initiated: db1d8764-9d10-4a29-a19a-c28ceef92bce [{"twilioCallSid":"CA459421aed4f0192a98664d5051848bb4","leadId":"2","leadPhoneNumber":"+923360150089"}]
[2025-06-23T13:40:09.875Z] [INFO] ************* - - [23/Jun/2025:13:40:09 +0000] "POST /api/calls/initiate HTTP/1.1" 201 225 "-" "okhttp/4.12.0" 
[2025-06-23T13:40:11.951Z] [INFO] ************* - - [23/Jun/2025:13:40:11 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 200 385 "-" "okhttp/4.12.0" 
[2025-06-23T13:40:13.974Z] [INFO] ************* - - [23/Jun/2025:13:40:13 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:16.000Z] [INFO] ************* - - [23/Jun/2025:13:40:16 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:17.997Z] [INFO] ************* - - [23/Jun/2025:13:40:17 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:19.999Z] [INFO] ************* - - [23/Jun/2025:13:40:19 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:21.982Z] [INFO] ************* - - [23/Jun/2025:13:40:21 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:23.996Z] [INFO] ************* - - [23/Jun/2025:13:40:23 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:26.034Z] [INFO] ************* - - [23/Jun/2025:13:40:26 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:28.018Z] [INFO] ************* - - [23/Jun/2025:13:40:28 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:30.032Z] [INFO] ************* - - [23/Jun/2025:13:40:30 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:32.046Z] [INFO] ************* - - [23/Jun/2025:13:40:32 +0000] "GET /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:40:32.095Z] [INFO] Call ended: db1d8764-9d10-4a29-a19a-c28ceef92bce [{"twilioCallSid":"CA459421aed4f0192a98664d5051848bb4"}]
[2025-06-23T13:40:32.097Z] [INFO] ************* - - [23/Jun/2025:13:40:32 +0000] "POST /api/calls/db1d8764-9d10-4a29-a19a-c28ceef92bce/end HTTP/1.1" 200 130 "-" "okhttp/4.12.0" 
[2025-06-23T13:42:31.745Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:42:31.748Z] [INFO] Environment: development 
[2025-06-23T13:42:31.749Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:42:46.318Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:42:46.320Z] [INFO] Environment: development 
[2025-06-23T13:42:46.321Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:42:59.183Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:42:59.186Z] [INFO] Environment: development 
[2025-06-23T13:42:59.187Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:43:40.895Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:43:40.898Z] [INFO] Environment: development 
[2025-06-23T13:43:40.899Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:45:27.918Z] [INFO] ************** - - [23/Jun/2025:13:45:27 +0000] "POST /api/calls/conference HTTP/1.1" 400 1334 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:45:42.672Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:45:42.674Z] [INFO] Environment: development 
[2025-06-23T13:45:42.675Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:45:55.191Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:45:55.193Z] [INFO] Environment: development 
[2025-06-23T13:45:55.193Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:46:10.321Z] [INFO] Conference call initiated: 9390d006-187d-43e6-bbf4-54e35e2c8296 [{"leadCallSid":"CA6756b5b12e51ff346d8fe3adc5603adc","callerCallSid":"CAc81e49c46aefb2e17d985cf755001f88","conferenceId":"conference-9390d006-187d-43e6-bbf4-54e35e2c8296","leadPhone":"+923360150089","callerPhone":"+923360150089"}]
[2025-06-23T13:46:10.324Z] [INFO] ************** - - [23/Jun/2025:13:46:10 +0000] "POST /api/calls/conference HTTP/1.1" 201 365 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:47:50.507Z] [INFO] Conference call initiated: 8195697b-541d-4afa-84a2-538b0334ff02 [{"leadCallSid":"CAbcdb1734019b2f06e57880d2af98de4a","callerCallSid":"CAe4e682c039d7408587dd20dfe97f3c25","conferenceId":"conference-8195697b-541d-4afa-84a2-538b0334ff02","leadPhone":"+923360150089","callerPhone":"+923360150089"}]
[2025-06-23T13:47:50.509Z] [INFO] ************* - - [23/Jun/2025:13:47:50 +0000] "POST /api/calls/conference HTTP/1.1" 201 374 "-" "okhttp/4.12.0" 
[2025-06-23T13:47:52.674Z] [INFO] ************* - - [23/Jun/2025:13:47:52 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 200 544 "-" "okhttp/4.12.0" 
[2025-06-23T13:47:54.756Z] [INFO] ************* - - [23/Jun/2025:13:47:54 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:47:56.887Z] [INFO] ************* - - [23/Jun/2025:13:47:56 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:47:59.002Z] [INFO] ************* - - [23/Jun/2025:13:47:59 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:01.151Z] [INFO] ************* - - [23/Jun/2025:13:48:01 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:03.273Z] [INFO] ************* - - [23/Jun/2025:13:48:03 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:05.397Z] [INFO] ************* - - [23/Jun/2025:13:48:05 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:07.530Z] [INFO] ************* - - [23/Jun/2025:13:48:07 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:09.661Z] [INFO] ************* - - [23/Jun/2025:13:48:09 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:11.802Z] [INFO] ************* - - [23/Jun/2025:13:48:11 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:13.978Z] [INFO] ************* - - [23/Jun/2025:13:48:13 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:16.134Z] [INFO] ************* - - [23/Jun/2025:13:48:16 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:18.360Z] [INFO] ************* - - [23/Jun/2025:13:48:18 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:20.447Z] [INFO] ************* - - [23/Jun/2025:13:48:20 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:22.561Z] [INFO] ************* - - [23/Jun/2025:13:48:22 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:24.663Z] [INFO] ************* - - [23/Jun/2025:13:48:24 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:26.868Z] [INFO] ************* - - [23/Jun/2025:13:48:26 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:48:28.981Z] [INFO] ************* - - [23/Jun/2025:13:48:28 +0000] "GET /api/calls/8195697b-541d-4afa-84a2-538b0334ff02 HTTP/1.1" 304 - "-" "okhttp/4.12.0" 
[2025-06-23T13:49:19.497Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:49:19.499Z] [INFO] Environment: development 
[2025-06-23T13:49:19.499Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:49:30.703Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:49:30.706Z] [INFO] Environment: development 
[2025-06-23T13:49:30.706Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:49:42.461Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:49:42.463Z] [INFO] Environment: development 
[2025-06-23T13:49:42.464Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:50:46.063Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:50:46.065Z] [INFO] Environment: development 
[2025-06-23T13:50:46.065Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:51:52.794Z] [INFO] Twilio VoIP Backend server running on port 3003 
[2025-06-23T13:51:52.797Z] [INFO] Environment: development 
[2025-06-23T13:51:52.797Z] [INFO] Server accessible at http://**************:3003 
[2025-06-23T13:52:13.059Z] [INFO] ************** - - [23/Jun/2025:13:52:13 +0000] "GET / HTTP/1.1" 404 27 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
[2025-06-23T13:52:35.408Z] [INFO] Conference call initiated: b987cbee-7d3d-4d2f-a821-2478b1e299cf [{"leadCallSid":"CAfc312cb319990a8ae17084c7e35f95eb","callerCallSid":"CA34ca7bf8fe356bfb4076bde70ce64461","conferenceId":"conference-b987cbee-7d3d-4d2f-a821-2478b1e299cf","leadPhone":"+923360150089","callerPhone":"+923360150089"}]
[2025-06-23T13:52:35.409Z] [INFO] ************** - - [23/Jun/2025:13:52:35 +0000] "POST /api/calls/conference HTTP/1.1" 201 359 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:53:12.002Z] [INFO] Conference call initiated: 5a4b25e0-9146-4d9f-987c-ff6e97b781f4 [{"leadCallSid":"CA30b9190e474624e9eb1dd1c08cb880df","callerCallSid":"CAb4d3878b4bc2bbea091ac4a15603151d","conferenceId":"conference-5a4b25e0-9146-4d9f-987c-ff6e97b781f4","leadPhone":"+923360150089","callerPhone":"+923360150089"}]
[2025-06-23T13:53:12.004Z] [INFO] ************** - - [23/Jun/2025:13:53:12 +0000] "POST /api/calls/conference HTTP/1.1" 201 361 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:54:26.908Z] [INFO] Using TwiML App: APa2edba98d4c21313fee0109523b57778 for outbound-only call 
[2025-06-23T13:54:27.408Z] [INFO] Call initiated: 2a3692e8-5ed6-4d9d-a20a-3e47f23ff4ee [{"twilioCallSid":"CA92509d9be94e718c64d6b17b72ebb175","leadId":"test-logging","leadPhoneNumber":"+923360150089"}]
[2025-06-23T13:54:27.410Z] [INFO] ************** - - [23/Jun/2025:13:54:27 +0000] "POST /api/calls/initiate HTTP/1.1" 201 213 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202" 
[2025-06-23T13:56:06.533Z] [INFO] Twilio VoIP Backend server running on port 3002 
[2025-06-23T13:56:06.535Z] [INFO] Environment: development 
[2025-06-23T13:56:06.535Z] [INFO] Server accessible at http://**************:3002 
[2025-06-23T13:57:09.499Z] [INFO] ************** - - [23/Jun/2025:13:57:09 +0000] "GET / HTTP/1.1" 404 27 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
