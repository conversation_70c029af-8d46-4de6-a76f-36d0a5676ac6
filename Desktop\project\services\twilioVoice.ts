import { Platform, PermissionsAndroid, Alert } from 'react-native';
import { setAudioModeAsync } from 'expo-audio';
import { apiService, CallData } from './api';

export interface TwilioVoiceConfig {
  accessToken: string;
  identity: string;
}

export interface CallState {
  isConnected: boolean;
  isOnHold: boolean;
  isMuted: boolean;
  callSid?: string;
  callId?: string;
  duration: number;
}

class TwilioVoiceService {
  private callState: CallState = {
    isConnected: false,
    isOnHold: false,
    isMuted: false,
    duration: 0
  };
  private callTimer: NodeJS.Timeout | null = null;
  private listeners: Array<(state: CallState) => void> = [];
  private currentCallData: CallData | null = null;

  constructor() {
    this.initializeAudio();
  }

  private async initializeAudio() {
    try {
      // Request permissions
      await this.requestPermissions();

      // Configure audio for calls
      try {
        await setAudioModeAsync({
          allowsRecording: true,
        });
      } catch (error) {
        console.log('Audio configuration not critical, continuing...', error);
      }

      console.log('Audio system initialized successfully');
    } catch (error) {
      console.error('Failed to initialize audio system:', error);
    }
  }

  private async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        const allGranted = Object.values(granted).every(
          permission => permission === PermissionsAndroid.RESULTS.GRANTED
        );

        if (!allGranted) {
          Alert.alert(
            'Permissions Required',
            'Microphone permission is required for voice calls.'
          );
          return false;
        }
      } else if (Platform.OS === 'ios') {
        // For iOS, permissions are handled through Info.plist
        // and will be requested automatically when audio is used
        console.log('iOS permissions will be requested when audio is accessed');
      } else {
        // For web/other platforms, assume permissions are available
        console.log('Web platform - no explicit permission request needed');
      }

      return true;
    } catch (error) {
      console.error('Permission request failed:', error);
      // Don't fail completely, just log the error
      return true;
    }
  }

  private async pollCallStatus() {
    if (!this.currentCallData) return;

    try {
      const response = await apiService.getCallDetails(this.currentCallData.callId);
      const updatedCall = response.data;

      // Update call state based on backend status
      switch (updatedCall.status) {
        case 'ringing':
          console.log('Call is ringing');
          break;
        case 'in-progress':
          console.log('Call connected');
          this.updateCallState({
            isConnected: true,
            callSid: updatedCall.twilioCallSid
          });
          this.startCallTimer();
          break;
        case 'completed':
        case 'failed':
        case 'busy':
        case 'no-answer':
          console.log('Call ended:', updatedCall.status);
          this.updateCallState({
            isConnected: false,
            callSid: undefined,
            callId: undefined
          });
          this.stopCallTimer();
          this.currentCallData = null;
          break;
      }
    } catch (error) {
      console.error('Failed to poll call status:', error);
    }
  }

  private startCallTimer() {
    this.callTimer = setInterval(() => {
      this.updateCallState({ 
        duration: this.callState.duration + 1 
      });
    }, 1000);
  }

  private stopCallTimer() {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
    this.updateCallState({ duration: 0 });
  }

  private updateCallState(updates: Partial<CallState>) {
    this.callState = { ...this.callState, ...updates };
    this.listeners.forEach(listener => listener(this.callState));
  }

  // Public methods
  public async makeCall(leadId: string, leadPhoneNumber: string, leadName: string): Promise<void> {
    try {
      // Initiate the call through our backend API
      const response = await apiService.initiateCall({
        leadId,
        leadPhoneNumber,
        leadName,
        callType: 'outbound',
        recordCall: true
      });

      console.log('Backend call initiated:', response.data);

      // Store the call data for tracking
      this.currentCallData = {
        callId: response.data.callId,
        twilioCallSid: response.data.twilioCallSid,
        leadId,
        leadName,
        leadPhoneNumber,
        twilioPhoneNumber: '', // Will be updated from status
        callType: 'outbound',
        status: 'initiated',
        recordCall: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.updateCallState({
        callId: response.data.callId,
        isConnected: false
      });

      // Start polling for call status updates
      this.startStatusPolling();

    } catch (error) {
      console.error('Failed to make call:', error);
      throw error;
    }
  }

  private statusPollingInterval: NodeJS.Timeout | null = null;

  private startStatusPolling() {
    // Poll every 2 seconds for call status updates
    this.statusPollingInterval = setInterval(() => {
      this.pollCallStatus();
    }, 2000);
  }

  private stopStatusPolling() {
    if (this.statusPollingInterval) {
      clearInterval(this.statusPollingInterval);
      this.statusPollingInterval = null;
    }
  }

  public async endCall(): Promise<void> {
    try {
      // End call through backend API
      if (this.callState.callId) {
        await apiService.endCall(this.callState.callId);
      }

      // Stop status polling
      this.stopStatusPolling();

      this.updateCallState({
        isConnected: false,
        callId: undefined,
        callSid: undefined
      });

      this.currentCallData = null;
    } catch (error) {
      console.error('Failed to end call:', error);
      throw error;
    }
  }

  public async toggleMute(): Promise<void> {
    // For now, this is a UI-only toggle since we're using backend API
    // In a full native implementation, this would control actual audio
    const newMuteState = !this.callState.isMuted;
    this.updateCallState({ isMuted: newMuteState });

    // You could implement actual mute functionality here if needed
    console.log(`Call ${newMuteState ? 'muted' : 'unmuted'}`);
  }

  public async toggleHold(): Promise<void> {
    // For now, this is a UI-only toggle since we're using backend API
    // In a full native implementation, this would control actual call hold
    const newHoldState = !this.callState.isOnHold;
    this.updateCallState({ isOnHold: newHoldState });

    // You could implement actual hold functionality here if needed
    console.log(`Call ${newHoldState ? 'on hold' : 'resumed'}`);
  }

  public getCallState(): CallState {
    return this.callState;
  }

  public addStateListener(listener: (state: CallState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  public async getAudioDevices(): Promise<string[]> {
    // Return available audio output options
    return ['Speaker', 'Earpiece', 'Bluetooth'];
  }

  public async selectAudioDevice(device: string): Promise<void> {
    console.log(`Audio device selected: ${device}`);
    // In a full implementation, this would switch audio routing
  }
}

export const twilioVoiceService = new TwilioVoiceService();
export default twilioVoiceService;
