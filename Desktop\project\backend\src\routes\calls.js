const express = require('express');
const { v4: uuidv4 } = require('uuid');
const {
  client,
  twilioConfig,
  formatPhoneNumber,
  validatePhoneNumber,
} = require('../config/twilio');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { validate, initiateCallSchema } = require('../middleware/validation');
const { logger } = require('../utils/logger');

const router = express.Router();

// In-memory call storage (in production, use a database)
const activeCalls = new Map();
const callHistory = new Map();

// POST /api/calls/initiate - Initiate a call
router.post(
  '/initiate',
  validate(initiateCallSchema),
  asyncHandler(async (req, res) => {
    const { leadId, leadPhoneNumber, leadName, callType, recordCall } =
      req.body;

    // Format and validate phone numbers
    const formattedLeadPhone = formatPhoneNumber(leadPhoneNumber);
    if (!validatePhoneNumber(formattedLeadPhone)) {
      throw new AppError('Invalid lead phone number format', 400);
    }

    const callId = uuidv4();
    const webhookUrl = `${twilioConfig.webhookBaseUrl}/api/webhooks/call-status`;

    try {
      // Prepare call parameters for outbound-only calling
      const callParams = {
        from: twilioConfig.phoneNumber,
        to: formattedLeadPhone,
        statusCallback: webhookUrl,
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
        statusCallbackMethod: 'POST',
        record: recordCall,
        timeout: 30,
        machineDetection: 'Enable',
      };

      // Use TwiML App if configured (preferred for outbound-only calls)
      if (twilioConfig.twimlAppSid) {
        callParams.applicationSid = twilioConfig.twimlAppSid;
        logger.info(
          `Using TwiML App: ${twilioConfig.twimlAppSid} for outbound-only call`
        );
      } else {
        // Fallback to webhook URL if no TwiML App configured
        callParams.url = `${twilioConfig.webhookBaseUrl}/api/webhooks/twiml/outbound`;
        logger.info(
          'Using webhook URL for outbound call (consider using TwiML App for better control)'
        );
      }

      // Add recording callback if recording is enabled
      if (recordCall) {
        callParams.recordingStatusCallback = `${twilioConfig.webhookBaseUrl}/api/webhooks/recording-status`;
      }

      // Create the outbound call using Twilio
      const call = await client.calls.create(callParams);

      // Store call information
      const callData = {
        callId,
        twilioCallSid: call.sid,
        leadId,
        leadName,
        leadPhoneNumber: formattedLeadPhone,
        twilioPhoneNumber: twilioConfig.phoneNumber,
        callType,
        status: 'initiated',
        recordCall,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      activeCalls.set(callId, callData);
      callHistory.set(callId, callData);

      logger.info(`Call initiated: ${callId}`, {
        twilioCallSid: call.sid,
        leadId,
        leadPhoneNumber: formattedLeadPhone,
      });

      res.status(201).json({
        success: true,
        data: {
          callId,
          twilioCallSid: call.sid,
          status: 'initiated',
          leadName,
          message: 'Call initiated successfully',
        },
      });
    } catch (error) {
      logger.error('Failed to initiate call:', error);

      if (error.code && error.code.toString().startsWith('2')) {
        throw new AppError(`Twilio Error: ${error.message}`, 400);
      }

      throw new AppError('Failed to initiate call', 500);
    }
  })
);

// GET /api/calls/:callId - Get call details
router.get(
  '/:callId',
  asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const callData = callHistory.get(callId);
    if (!callData) {
      throw new AppError('Call not found', 404);
    }

    res.json({
      success: true,
      data: callData,
    });
  })
);

// GET /api/calls - Get all calls with pagination
router.get(
  '/',
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, status, leadId } = req.query;

    let calls = Array.from(callHistory.values());

    // Filter by status
    if (status) {
      calls = calls.filter((call) => call.status === status);
    }

    // Filter by leadId
    if (leadId) {
      calls = calls.filter((call) => call.leadId === leadId);
    }

    // Sort by creation date (newest first)
    calls.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedCalls = calls.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        calls: paginatedCalls,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(calls.length / limit),
          totalCalls: calls.length,
          hasNext: endIndex < calls.length,
          hasPrev: startIndex > 0,
        },
      },
    });
  })
);

// POST /api/calls/:callId/end - End an active call
router.post(
  '/:callId/end',
  asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const callData = activeCalls.get(callId);
    if (!callData) {
      throw new AppError('Active call not found', 404);
    }

    try {
      // End the call using Twilio
      await client
        .calls(callData.twilioCallSid)
        .update({ status: 'completed' });

      // Update call status
      callData.status = 'completed';
      callData.endedAt = new Date().toISOString();
      callData.updatedAt = new Date().toISOString();

      // Remove from active calls
      activeCalls.delete(callId);

      // Update call history
      callHistory.set(callId, callData);

      logger.info(`Call ended: ${callId}`, {
        twilioCallSid: callData.twilioCallSid,
      });

      res.json({
        success: true,
        data: {
          callId,
          status: 'completed',
          message: 'Call ended successfully',
        },
      });
    } catch (error) {
      logger.error('Failed to end call:', error);
      throw new AppError('Failed to end call', 500);
    }
  })
);

// GET /api/calls/:callId/recording - Get call recording details
router.get(
  '/:callId/recording',
  asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const callData = callHistory.get(callId);
    if (!callData) {
      throw new AppError('Call not found', 404);
    }

    if (!callData.recordingSid) {
      throw new AppError('No recording available for this call', 404);
    }

    try {
      // Fetch recording details from Twilio
      const recording = await client.recordings(callData.recordingSid).fetch();

      res.json({
        success: true,
        data: {
          callId,
          recordingSid: recording.sid,
          recordingUrl: `https://api.twilio.com${recording.uri.replace(
            '.json',
            '.mp3'
          )}`,
          duration: recording.duration,
          status: recording.status,
          dateCreated: recording.dateCreated,
          fileSize: recording.fileSize,
        },
      });
    } catch (error) {
      logger.error('Failed to fetch recording details:', error);
      throw new AppError('Failed to fetch recording details', 500);
    }
  })
);

// DELETE /api/calls/:callId/recording - Delete call recording
router.delete(
  '/:callId/recording',
  asyncHandler(async (req, res) => {
    const { callId } = req.params;

    const callData = callHistory.get(callId);
    if (!callData) {
      throw new AppError('Call not found', 404);
    }

    if (!callData.recordingSid) {
      throw new AppError('No recording available for this call', 404);
    }

    try {
      // Delete recording from Twilio
      await client.recordings(callData.recordingSid).remove();

      // Update call data
      callData.recordingSid = null;
      callData.recordingUrl = null;
      callData.recordingDeleted = true;
      callData.recordingDeletedAt = new Date().toISOString();
      callData.updatedAt = new Date().toISOString();

      callHistory.set(callId, callData);

      logger.info(`Recording deleted for call: ${callId}`);

      res.json({
        success: true,
        data: {
          callId,
          message: 'Recording deleted successfully',
        },
      });
    } catch (error) {
      logger.error('Failed to delete recording:', error);
      throw new AppError('Failed to delete recording', 500);
    }
  })
);

// GET /api/calls/active - Get all active calls
router.get(
  '/active/list',
  asyncHandler(async (req, res) => {
    const activeCalls = Array.from(activeCalls.values());

    res.json({
      success: true,
      data: {
        activeCalls,
        count: activeCalls.length,
      },
    });
  })
);

// GET /api/calls/stats - Get call statistics
router.get(
  '/stats/summary',
  asyncHandler(async (req, res) => {
    const { startDate, endDate } = req.query;

    let calls = Array.from(callHistory.values());

    // Filter by date range if provided
    if (startDate || endDate) {
      calls = calls.filter((call) => {
        const callDate = new Date(call.createdAt);
        if (startDate && callDate < new Date(startDate)) return false;
        if (endDate && callDate > new Date(endDate)) return false;
        return true;
      });
    }

    // Calculate statistics
    const stats = {
      totalCalls: calls.length,
      completedCalls: calls.filter((call) => call.status === 'completed')
        .length,
      failedCalls: calls.filter((call) =>
        ['failed', 'busy', 'no-answer'].includes(call.status)
      ).length,
      activeCalls: calls.filter((call) =>
        ['ringing', 'in-progress'].includes(call.status)
      ).length,
      averageDuration: 0,
      totalDuration: 0,
      recordedCalls: calls.filter((call) => call.recordingSid).length,
    };

    // Calculate average duration for completed calls
    const completedCallsWithDuration = calls.filter(
      (call) => call.duration && call.status === 'completed'
    );
    if (completedCallsWithDuration.length > 0) {
      const totalDuration = completedCallsWithDuration.reduce(
        (sum, call) => sum + parseInt(call.duration || 0),
        0
      );
      stats.totalDuration = totalDuration;
      stats.averageDuration = Math.round(
        totalDuration / completedCallsWithDuration.length
      );
    }

    res.json({
      success: true,
      data: stats,
    });
  })
);

// POST /api/calls/:callId/join - Join an active call for real-time communication
router.post(
  '/:callId/join',
  asyncHandler(async (req, res) => {
    const { callId } = req.params;
    const { callerPhoneNumber } = req.body;

    if (!callerPhoneNumber) {
      throw new AppError('Caller phone number is required', 400);
    }

    const callData = activeCalls.get(callId) || callHistory.get(callId);
    if (!callData) {
      throw new AppError('Call not found', 404);
    }

    // Format and validate caller phone number
    const formattedCallerPhone = formatPhoneNumber(callerPhoneNumber);
    if (!validatePhoneNumber(formattedCallerPhone)) {
      throw new AppError('Invalid caller phone number format', 400);
    }

    try {
      // Create a call to the caller to join the conference
      const callerCall = await client.calls.create({
        from: twilioConfig.phoneNumber,
        to: formattedCallerPhone,
        url: `${twilioConfig.webhookBaseUrl}/api/webhooks/twiml/join-conference/${callData.twilioCallSid}`,
        statusCallback: `${twilioConfig.webhookBaseUrl}/api/webhooks/call-status`,
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
        statusCallbackMethod: 'POST',
      });

      // Update call data with caller information
      callData.callerPhoneNumber = formattedCallerPhone;
      callData.callerCallSid = callerCall.sid;
      callData.updatedAt = new Date().toISOString();

      activeCalls.set(callId, callData);
      callHistory.set(callId, callData);

      logger.info(`Caller joining conference for call: ${callId}`, {
        callerCallSid: callerCall.sid,
        callerPhone: formattedCallerPhone,
        conferenceId: callData.twilioCallSid,
      });

      res.json({
        success: true,
        data: {
          callId,
          callerCallSid: callerCall.sid,
          message: 'Joining call - please answer your phone to connect',
        },
      });
    } catch (error) {
      logger.error('Failed to join call:', error);

      if (error.code && error.code.toString().startsWith('2')) {
        throw new AppError(`Twilio Error: ${error.message}`, 400);
      }

      throw new AppError('Failed to join call', 500);
    }
  })
);

module.exports = router;
