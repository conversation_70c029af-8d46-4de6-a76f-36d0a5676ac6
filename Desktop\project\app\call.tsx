import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Phone, PhoneOff } from 'lucide-react-native';
import { mockLeads } from './(tabs)/index';
import { apiService, CallData } from '../services/api';

export default function CallScreen() {
  const { leadId } = useLocalSearchParams<{ leadId: string }>();
  const [isCallActive, setIsCallActive] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [callData, setCallData] = useState<CallData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [callStatus, setCallStatus] = useState<string>('Ready to call');

  const lead = mockLeads.find(l => l.id === leadId);

  // Poll for call status updates when call is active
  useEffect(() => {
    let interval: NodeJS.Timeout;
    let statusInterval: NodeJS.Timeout;

    if (isCallActive && callData) {
      // Update call duration
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);

      // Poll call status every 2 seconds
      statusInterval = setInterval(async () => {
        try {
          const response = await apiService.getCallDetails(callData.callId);
          const updatedCall = response.data;
          setCallData(updatedCall);

          // Update status display
          switch (updatedCall.status) {
            case 'ringing':
              setCallStatus('Ringing...');
              break;
            case 'in-progress':
              setCallStatus('Call in progress');
              break;
            case 'completed':
              setCallStatus('Call completed');
              setIsCallActive(false);
              break;
            case 'failed':
            case 'busy':
            case 'no-answer':
              setCallStatus(`Call ${updatedCall.status}`);
              setIsCallActive(false);
              break;
          }
        } catch (error) {
          console.error('Failed to fetch call status:', error);
        }
      }, 2000);
    }

    return () => {
      if (interval) clearInterval(interval);
      if (statusInterval) clearInterval(statusInterval);
    };
  }, [isCallActive, callData]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartCall = async () => {
    if (!lead) {
      Alert.alert('Error', 'Lead information not found');
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService.initiateCall({
        leadId: lead.id,
        leadPhoneNumber: lead.phone,
        leadName: lead.title,
        callType: 'outbound',
        recordCall: true
      });

      setCallData({
        callId: response.data.callId,
        twilioCallSid: response.data.twilioCallSid,
        leadId: lead.id,
        leadName: lead.title,
        leadPhoneNumber: lead.phone,
        twilioPhoneNumber: '', // Will be updated from status
        callType: 'outbound',
        status: 'initiated',
        recordCall: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      setIsCallActive(true);
      setCallDuration(0);
      setCallStatus('Initiating call...');

    } catch (error) {
      console.error('Failed to initiate call:', error);
      Alert.alert(
        'Call Failed',
        error instanceof Error ? error.message : 'Failed to start the call. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleEndCall = async () => {
    if (callData) {
      try {
        await apiService.endCall(callData.callId);
      } catch (error) {
        console.error('Failed to end call:', error);
      }
    }

    setIsCallActive(false);
    setCallDuration(0);
    setCallData(null);
    setCallStatus('Ready to call');
    router.back();
  };

  const handleBack = () => {
    if (isCallActive) {
      handleEndCall();
    } else {
      router.back();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Voice Call</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        <View style={styles.callInfo}>
          <Text style={styles.leadTitle}>{lead?.title}</Text>
          <Text style={styles.leadPhone}>{lead?.phone}</Text>
          <Text style={styles.callStatus}>{callStatus}</Text>
          {isCallActive && (
            <Text style={styles.callTimer}>{formatTime(callDuration)}</Text>
          )}
          {callData && (
            <Text style={styles.callId}>Call ID: {callData.callId.slice(0, 8)}...</Text>
          )}
        </View>

        <View style={styles.callControls}>
          {!isCallActive ? (
            <TouchableOpacity
              style={[styles.startCallButton, isLoading && styles.disabledButton]}
              onPress={handleStartCall}
              activeOpacity={0.8}
              disabled={isLoading}
            >
              <Phone size={32} color="#FFFFFF" />
              <Text style={styles.startCallText}>
                {isLoading ? 'Starting...' : 'Start Call'}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.endCallButton}
              onPress={handleEndCall}
              activeOpacity={0.8}
            >
              <PhoneOff size={32} color="#FFFFFF" />
              <Text style={styles.endCallText}>End Call</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.securityNote}>
          <Text style={styles.securityText}>
            🔒 All call details are handled securely through our backend system. 
            Client phone numbers are never exposed in the app.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C1C1E',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  callInfo: {
    alignItems: 'center',
    marginTop: 60,
  },
  leadTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  leadPhone: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 16,
  },
  callStatus: {
    fontSize: 18,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 24,
  },
  callId: {
    fontSize: 12,
    color: '#6D6D70',
    textAlign: 'center',
    marginTop: 8,
  },
  callTimer: {
    fontSize: 48,
    fontWeight: '300',
    color: '#FFFFFF',
    fontVariant: ['tabular-nums'],
  },
  callControls: {
    alignItems: 'center',
    marginBottom: 60,
  },
  startCallButton: {
    backgroundColor: '#34C759',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#34C759',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  endCallButton: {
    backgroundColor: '#FF3B30',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FF3B30',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  endCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  securityNote: {
    backgroundColor: 'rgba(142, 142, 147, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  securityText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 20,
  },
});