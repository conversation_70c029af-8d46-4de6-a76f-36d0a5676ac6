import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Phone, PhoneOff, Mic, MicOff, Volume2 } from 'lucide-react-native';
import { mockLeads } from './(tabs)/index';
import { apiService, CallData } from '../services/api';
import twilioVoiceService, { CallState } from '../services/twilioVoice';

export default function CallScreen() {
  const { leadId } = useLocalSearchParams<{ leadId: string }>();
  const [isCallActive, setIsCallActive] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [callData, setCallData] = useState<CallData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [callStatus, setCallStatus] = useState<string>('Ready to call');
  const [twilioCallState, setTwilioCallState] = useState<CallState | null>(null);

  const lead = mockLeads.find(l => l.id === leadId);

  // Set up Twilio Voice service listener
  useEffect(() => {
    const unsubscribe = twilioVoiceService.addStateListener((state: CallState) => {
      setTwilioCallState(state);
      setCallDuration(state.duration);

      if (state.isConnected) {
        setCallStatus('Call in progress');
        setIsCallActive(true);
      } else if (state.callId) {
        setCallStatus('Connecting...');
      }
    });

    return unsubscribe;
  }, []);

  // Poll for call status updates when call is active
  useEffect(() => {
    let statusInterval: NodeJS.Timeout;

    if (isCallActive && callData) {
      // Poll call status every 2 seconds
      statusInterval = setInterval(async () => {
        try {
          const response = await apiService.getCallDetails(callData.callId);
          const updatedCall = response.data;
          setCallData(updatedCall);

          // Update status display
          switch (updatedCall.status) {
            case 'ringing':
              setCallStatus('Ringing...');
              break;
            case 'in-progress':
              setCallStatus('Call in progress');
              break;
            case 'completed':
              setCallStatus('Call completed');
              setIsCallActive(false);
              break;
            case 'failed':
            case 'busy':
            case 'no-answer':
              setCallStatus(`Call ${updatedCall.status}`);
              setIsCallActive(false);
              break;
          }
        } catch (error) {
          console.error('Failed to fetch call status:', error);
        }
      }, 2000);
    }

    return () => {
      if (statusInterval) clearInterval(statusInterval);
    };
  }, [isCallActive, callData]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartCall = async () => {
    if (!lead) {
      Alert.alert('Error', 'Lead information not found');
      return;
    }

    setIsLoading(true);
    try {
      // Use native Twilio service to make the call
      await twilioVoiceService.makeCall(lead.id, lead.phone, lead.title);

      setCallStatus('Initiating call...');
      setIsCallActive(true);
      setCallDuration(0);

    } catch (error) {
      console.error('Failed to initiate call:', error);
      Alert.alert(
        'Call Failed',
        error instanceof Error ? error.message : 'Failed to start the call. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleEndCall = async () => {
    try {
      // Use native Twilio service to end the call
      await twilioVoiceService.endCall();
    } catch (error) {
      console.error('Failed to end call:', error);
    }

    setIsCallActive(false);
    setCallDuration(0);
    setCallData(null);
    setCallStatus('Ready to call');
    router.back();
  };

  const handleToggleMute = async () => {
    try {
      await twilioVoiceService.toggleMute();
    } catch (error) {
      console.error('Failed to toggle mute:', error);
    }
  };

  const handleToggleHold = async () => {
    try {
      await twilioVoiceService.toggleHold();
    } catch (error) {
      console.error('Failed to toggle hold:', error);
    }
  };

  const handleBack = () => {
    if (isCallActive) {
      handleEndCall();
    } else {
      router.back();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Voice Call</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        <View style={styles.callInfo}>
          <Text style={styles.leadTitle}>{lead?.title}</Text>
          <Text style={styles.leadPhone}>{lead?.phone}</Text>
          <Text style={styles.callStatus}>{callStatus}</Text>
          {isCallActive && (
            <Text style={styles.callTimer}>{formatTime(callDuration)}</Text>
          )}
          {callData && (
            <Text style={styles.callId}>Call ID: {callData.callId.slice(0, 8)}...</Text>
          )}
        </View>

        <View style={styles.callControls}>
          {!isCallActive ? (
            <TouchableOpacity
              style={[styles.startCallButton, isLoading && styles.disabledButton]}
              onPress={handleStartCall}
              activeOpacity={0.8}
              disabled={isLoading}
            >
              <Phone size={32} color="#FFFFFF" />
              <Text style={styles.startCallText}>
                {isLoading ? 'Starting...' : 'Start Call'}
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.activeCallControls}>
              {/* Call control buttons */}
              <View style={styles.callActionButtons}>
                <TouchableOpacity
                  style={[styles.actionButton, twilioCallState?.isMuted && styles.activeActionButton]}
                  onPress={handleToggleMute}
                  activeOpacity={0.8}
                >
                  {twilioCallState?.isMuted ? (
                    <MicOff size={24} color="#FFFFFF" />
                  ) : (
                    <Mic size={24} color="#FFFFFF" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.actionButton, twilioCallState?.isOnHold && styles.activeActionButton]}
                  onPress={handleToggleHold}
                  activeOpacity={0.8}
                >
                  <Volume2 size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </View>

              {/* End call button */}
              <TouchableOpacity
                style={styles.endCallButton}
                onPress={handleEndCall}
                activeOpacity={0.8}
              >
                <PhoneOff size={32} color="#FFFFFF" />
                <Text style={styles.endCallText}>End Call</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        <View style={styles.securityNote}>
          <Text style={styles.securityText}>
            🔒 All call details are handled securely through our backend system. 
            Client phone numbers are never exposed in the app.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C1C1E',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  callInfo: {
    alignItems: 'center',
    marginTop: 60,
  },
  leadTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  leadPhone: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 16,
  },
  callStatus: {
    fontSize: 18,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 24,
  },
  callId: {
    fontSize: 12,
    color: '#6D6D70',
    textAlign: 'center',
    marginTop: 8,
  },
  callTimer: {
    fontSize: 48,
    fontWeight: '300',
    color: '#FFFFFF',
    fontVariant: ['tabular-nums'],
  },
  callControls: {
    alignItems: 'center',
    marginBottom: 60,
  },
  startCallButton: {
    backgroundColor: '#34C759',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#34C759',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  endCallButton: {
    backgroundColor: '#FF3B30',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FF3B30',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  endCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  activeCallControls: {
    alignItems: 'center',
    width: '100%',
  },
  callActionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 40,
    gap: 30,
  },
  actionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  activeActionButton: {
    backgroundColor: '#007AFF',
  },
  securityNote: {
    backgroundColor: 'rgba(142, 142, 147, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  securityText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 20,
  },
});