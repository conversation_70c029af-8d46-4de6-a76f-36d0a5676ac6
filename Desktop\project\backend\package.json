{"name": "twilio-voip-backend", "version": "1.0.0", "description": "Backend API for Twilio VoIP integration with real-time calling", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "keywords": ["twi<PERSON>", "voip", "api", "calling"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "twilio": "^4.19.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}