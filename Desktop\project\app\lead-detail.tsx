import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  ScrollView,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Phone, MessageCircle, Mail, Clock, MapPin, Star } from 'lucide-react-native';
import { mockLeads } from './(tabs)/index';
import AnimatedButton from '@/components/AnimatedButton';
import GradientBackground from '@/components/GradientBackground';

export default function LeadDetailScreen() {
  const { leadId } = useLocalSearchParams<{ leadId: string }>();
  
  const lead = mockLeads.find(l => l.id === leadId);

  if (!lead) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Lead not found</Text>
          <AnimatedButton
            title="Go Back"
            onPress={() => router.back()}
            style={styles.errorButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  const handleBack = () => {
    router.back();
  };

  const handleCall = () => {
    router.push({
      pathname: '/call',
      params: { leadId: lead.id }
    });
  };

  const handleWhatsApp = () => {
    router.push({
      pathname: '/chat',
      params: { leadId: lead.id }
    });
  };

  const handleEmail = () => {
    router.push({
      pathname: '/email',
      params: { leadId: lead.id }
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#FF3B30';
      case 'medium': return '#FF9500';
      case 'low': return '#34C759';
      default: return '#8E8E93';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <AnimatedButton
          title=""
          onPress={handleBack}
          style={styles.backButton}
        >
          <ArrowLeft size={24} color="#007AFF" />
        </AnimatedButton>
        <Text style={styles.headerTitle}>Lead Details</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <GradientBackground
          colors={['#667eea', '#764ba2']}
          style={styles.heroSection}
        >
          <Image source={{ uri: lead.avatar }} style={styles.heroAvatar} />
          <Text style={styles.heroTitle}>{lead.title}</Text>
          <View style={styles.heroMeta}>
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(lead.priority) }]}>
              <Text style={styles.priorityText}>
                {lead.priority.toUpperCase()} PRIORITY
              </Text>
            </View>
          </View>
        </GradientBackground>

        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Clock size={20} color="#8E8E93" />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Received</Text>
                <Text style={styles.infoValue}>{formatDate(lead.date)}</Text>
              </View>
            </View>
            
            <View style={styles.infoRow}>
              <MapPin size={20} color="#8E8E93" />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Source</Text>
                <Text style={styles.infoValue}>{lead.source}</Text>
              </View>
            </View>

            <View style={styles.infoRow}>
              <Star size={20} color="#8E8E93" />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Status</Text>
                <Text style={styles.infoValue}>New Lead</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>Contact Actions</Text>
          <Text style={styles.sectionSubtitle}>
            Choose how you'd like to reach out to this lead
          </Text>

          <View style={styles.actionsGrid}>
            <AnimatedButton
              title=""
              onPress={handleCall}
              style={[styles.actionCard, styles.callCard]}
            >
              <View style={styles.actionIcon}>
                <Phone size={28} color="#FFFFFF" />
              </View>
              <Text style={styles.actionTitle}>Voice Call</Text>
              <Text style={styles.actionSubtitle}>Start a phone conversation</Text>
            </AnimatedButton>

            <AnimatedButton
              title=""
              onPress={handleWhatsApp}
              style={[styles.actionCard, styles.whatsappCard]}
            >
              <View style={styles.actionIcon}>
                <MessageCircle size={28} color="#FFFFFF" />
              </View>
              <Text style={styles.actionTitle}>WhatsApp</Text>
              <Text style={styles.actionSubtitle}>Send a message</Text>
            </AnimatedButton>

            <AnimatedButton
              title=""
              onPress={handleEmail}
              style={[styles.actionCard, styles.emailCard]}
            >
              <View style={styles.actionIcon}>
                <Mail size={28} color="#FFFFFF" />
              </View>
              <Text style={styles.actionTitle}>Email</Text>
              <Text style={styles.actionSubtitle}>Compose an email</Text>
            </AnimatedButton>
          </View>
        </View>

        <View style={styles.securitySection}>
          <View style={styles.securityCard}>
            <Text style={styles.securityTitle}>🔒 Privacy Protected</Text>
            <Text style={styles.securityText}>
              All contact information is securely stored and never exposed in the app interface. 
              Communications are handled through our encrypted backend services.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    backgroundColor: '#F2F2F7',
    width: 44,
    height: 44,
    borderRadius: 12,
    shadowOpacity: 0,
    elevation: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  placeholder: {
    width: 44,
  },
  content: {
    flex: 1,
  },
  heroSection: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 24,
  },
  heroAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  heroMeta: {
    alignItems: 'center',
  },
  priorityBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  infoSection: {
    padding: 16,
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  actionsSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 20,
  },
  actionsGrid: {
    gap: 16,
  },
  actionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  callCard: {
    backgroundColor: '#34C759',
  },
  whatsappCard: {
    backgroundColor: '#25D366',
  },
  emailCard: {
    backgroundColor: '#007AFF',
  },
  actionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  actionSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  securitySection: {
    padding: 16,
    paddingBottom: 32,
  },
  securityCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#34C759',
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  securityText: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    color: '#8E8E93',
    marginBottom: 24,
  },
  errorButton: {
    backgroundColor: '#007AFF',
  },
});