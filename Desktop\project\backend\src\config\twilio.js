const twilio = require('twilio');
const { logger } = require('../utils/logger');

// Validate required environment variables
const requiredEnvVars = [
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'TWILIO_PHONE_NUMBER',
];
const optionalEnvVars = ['TWILIO_TWIML_APP_SID']; // Optional TwiML App SID
const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);

if (missingVars.length > 0) {
  logger.error(
    `Missing required environment variables: ${missingVars.join(', ')}`
  );
  process.exit(1);
}

// Initialize Twilio client
const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Twilio configuration
const twilioConfig = {
  accountSid: process.env.TWILIO_ACCOUNT_SID,
  authToken: process.env.TWILIO_AUTH_TOKEN,
  phoneNumber: process.env.TWILIO_PHONE_NUMBER,
  twimlAppSid: process.env.TWILIO_TWIML_APP_SID, // Optional TwiML App SID
  webhookBaseUrl: process.env.WEBHOOK_BASE_URL || 'http://localhost:3001',
};

// Test Twilio connection
const testTwilioConnection = async () => {
  try {
    await client.api.accounts(twilioConfig.accountSid).fetch();
    logger.info('Twilio connection established successfully');
    return true;
  } catch (error) {
    logger.error('Failed to connect to Twilio:', error.message);
    return false;
  }
};

// Validate phone number format
const validatePhoneNumber = (phoneNumber) => {
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
};

// Format phone number to E.164 format
const formatPhoneNumber = (phoneNumber) => {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Add country code if missing (assuming US +1)
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+${cleaned}`;
  }

  return phoneNumber.startsWith('+') ? phoneNumber : `+${phoneNumber}`;
};

module.exports = {
  client,
  twilioConfig,
  testTwilioConnection,
  validatePhoneNumber,
  formatPhoneNumber,
};
