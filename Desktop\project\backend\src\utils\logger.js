const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Log levels
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3
};

const currentLogLevel = LOG_LEVELS[process.env.LOG_LEVEL] || LOG_LEVELS.info;

// Create logger
const logger = {
  error: (message, ...args) => log('error', message, ...args),
  warn: (message, ...args) => log('warn', message, ...args),
  info: (message, ...args) => log('info', message, ...args),
  debug: (message, ...args) => log('debug', message, ...args)
};

function log(level, message, ...args) {
  if (LOG_LEVELS[level] > currentLogLevel) return;

  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  
  // Console output with colors
  const colors = {
    error: '\x1b[31m', // Red
    warn: '\x1b[33m',  // Yellow
    info: '\x1b[36m',  // Cyan
    debug: '\x1b[90m'  // Gray
  };
  
  console.log(`${colors[level]}${logMessage}\x1b[0m`, ...args);
  
  // File output
  const logFile = path.join(logsDir, `${level}.log`);
  const fileMessage = `${logMessage} ${args.length > 0 ? JSON.stringify(args) : ''}\n`;
  
  fs.appendFileSync(logFile, fileMessage);
  
  // Also write to combined log
  const combinedLogFile = path.join(logsDir, 'combined.log');
  fs.appendFileSync(combinedLogFile, fileMessage);
}

module.exports = { logger };
